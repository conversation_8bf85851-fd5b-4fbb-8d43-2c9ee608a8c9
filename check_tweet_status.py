#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查推文状态脚本
验证推文是否真的存在以及账号状态
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from selenium.webdriver.common.by import By
from src.core.browser_manager import BrowserPool
from src.core.account_manager import AccountManager
from src.database.connection import init_database
from src.utils.logger import get_logger

logger = get_logger(__name__)

async def check_tweet_exists(tweet_url: str):
    """检查推文是否存在"""
    browser_pool = BrowserPool()

    try:
        # 创建浏览器实例
        driver_wrapper = await browser_pool.get_driver(
            account_id=999,  # 临时ID
            headless=False,  # 显示浏览器以便观察
            proxy=None
        )
        
        logger.info(f"正在访问推文: {tweet_url}")
        await driver_wrapper.get(tweet_url)
        await asyncio.sleep(5)
        
        # 检查页面标题和内容
        title = await driver_wrapper.execute_async(
            lambda: driver_wrapper.driver.title
        )
        logger.info(f"页面标题: {title}")
        
        # 检查是否有推文内容
        try:
            tweet_text_elements = await driver_wrapper.find_elements(
                By.CSS_SELECTOR, '[data-testid="tweetText"]'
            )
            
            if tweet_text_elements:
                for element in tweet_text_elements:
                    text = await driver_wrapper.execute_async(
                        lambda: element.text
                    )
                    logger.info(f"找到推文内容: {text}")
            else:
                logger.warning("未找到推文内容元素")
                
                # 检查是否有错误信息
                error_elements = await driver_wrapper.find_elements(
                    By.CSS_SELECTOR, '[data-testid="error"]'
                )
                if error_elements:
                    for element in error_elements:
                        error_text = await driver_wrapper.execute_async(
                            lambda: element.text
                        )
                        logger.error(f"发现错误信息: {error_text}")

                # 检查是否显示"推文不存在"
                not_found_elements = await driver_wrapper.find_elements(
                    By.XPATH, "//*[contains(text(), 'not available') or contains(text(), 'deleted') or contains(text(), 'suspended')]"
                )
                if not_found_elements:
                    logger.error("推文可能已被删除或账号被暂停")
        
        except Exception as e:
            logger.error(f"检查推文内容时出错: {e}")
        
        # 等待用户观察
        input("按回车键继续...")
        
    except Exception as e:
        logger.error(f"检查推文时出错: {e}")
    finally:
        await browser_pool.release_driver(999)

async def check_account_status(username: str):
    """检查账号状态"""
    browser_pool = BrowserPool()

    try:
        # 创建浏览器实例
        driver_wrapper = await browser_pool.get_driver(
            account_id=998,  # 临时ID
            headless=False,
            proxy=None
        )
        
        profile_url = f"https://x.com/{username}"
        logger.info(f"正在访问账号主页: {profile_url}")
        await driver_wrapper.get(profile_url)
        await asyncio.sleep(5)
        
        # 检查页面标题
        title = await driver_wrapper.execute_async(
            lambda: driver_wrapper.driver.title
        )
        logger.info(f"账号页面标题: {title}")
        
        # 检查账号状态
        try:
            # 检查是否被暂停
            suspended_elements = await driver_wrapper.find_elements(
                By.XPATH, "//*[contains(text(), 'suspended') or contains(text(), 'Account suspended')]"
            )
            if suspended_elements:
                logger.error("账号已被暂停")
                return

            # 检查是否存在
            not_found_elements = await driver_wrapper.find_elements(
                By.XPATH, "//*[contains(text(), 'not exist') or contains(text(), 'not found')]"
            )
            if not_found_elements:
                logger.error("账号不存在")
                return

            # 检查推文数量
            tweet_count_elements = await driver_wrapper.find_elements(
                By.CSS_SELECTOR, '[data-testid="UserTweets"] [role="tablist"] a'
            )
            if tweet_count_elements:
                for element in tweet_count_elements:
                    text = await driver_wrapper.execute_async(
                        lambda: element.text
                    )
                    if "posts" in text.lower() or "tweets" in text.lower():
                        logger.info(f"账号推文信息: {text}")
            
            logger.info("账号状态正常")
            
        except Exception as e:
            logger.error(f"检查账号状态时出错: {e}")
        
        # 等待用户观察
        input("按回车键继续...")
        
    except Exception as e:
        logger.error(f"检查账号时出错: {e}")
    finally:
        await browser_pool.release_driver(998)

async def main():
    """主函数"""
    # 初始化数据库
    init_database()
    
    # 从日志中获取的信息
    tweet_url = "https://x.com/AnthonyGon78058/status/1952045432521232414"
    username = "AnthonyGon78058"
    
    print("=" * 60)
    print("推文状态检查工具")
    print("=" * 60)
    
    print(f"推文URL: {tweet_url}")
    print(f"账号: {username}")
    print()
    
    # 检查推文是否存在
    print("1. 检查推文是否存在...")
    await check_tweet_exists(tweet_url)
    
    print("\n2. 检查账号状态...")
    await check_account_status(username)
    
    print("\n检查完成！")

if __name__ == "__main__":
    asyncio.run(main())
