#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的推文检查脚本
直接使用浏览器访问推文链接
"""

import time
import webbrowser
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager

def check_tweet_simple():
    """简单检查推文"""
    tweet_url = "https://x.com/AnthonyGon78058/status/1952045432521232414"
    account_url = "https://x.com/AnthonyGon78058"
    
    print("=" * 60)
    print("推文状态检查")
    print("=" * 60)
    print(f"推文URL: {tweet_url}")
    print(f"账号URL: {account_url}")
    print()
    
    # 设置Chrome选项
    chrome_options = Options()
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-blink-features=AutomationControlled")
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    
    # 创建WebDriver
    try:
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=chrome_options)
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        
        print("1. 检查推文是否存在...")
        driver.get(tweet_url)
        time.sleep(5)
        
        # 检查页面标题
        title = driver.title
        print(f"页面标题: {title}")
        
        # 检查是否有推文内容
        try:
            # 等待页面加载
            WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            
            # 检查推文文本
            tweet_elements = driver.find_elements(By.CSS_SELECTOR, '[data-testid="tweetText"]')
            if tweet_elements:
                print("✅ 找到推文内容:")
                for element in tweet_elements:
                    text = element.text
                    print(f"   内容: {text}")
            else:
                print("❌ 未找到推文内容")
                
                # 检查错误信息
                error_indicators = [
                    "This post is from an account that no longer exists",
                    "This Tweet was deleted",
                    "This account has been suspended",
                    "Something went wrong",
                    "Try again"
                ]
                
                page_text = driver.page_source
                for indicator in error_indicators:
                    if indicator.lower() in page_text.lower():
                        print(f"❌ 发现错误: {indicator}")
                        break
                else:
                    print("⚠️ 页面加载但未找到推文内容，可能需要登录")
        
        except Exception as e:
            print(f"检查推文时出错: {e}")
        
        input("\n按回车键继续检查账号状态...")
        
        print("\n2. 检查账号状态...")
        driver.get(account_url)
        time.sleep(5)
        
        # 检查账号页面
        title = driver.title
        print(f"账号页面标题: {title}")
        
        try:
            # 检查账号状态
            page_text = driver.page_source.lower()
            
            if "suspended" in page_text:
                print("❌ 账号已被暂停")
            elif "doesn't exist" in page_text or "not found" in page_text:
                print("❌ 账号不存在")
            else:
                print("✅ 账号状态正常")
                
                # 尝试获取推文数量
                try:
                    posts_elements = driver.find_elements(By.XPATH, "//*[contains(text(), 'posts') or contains(text(), 'Posts')]")
                    if posts_elements:
                        for element in posts_elements:
                            text = element.text
                            if any(char.isdigit() for char in text):
                                print(f"📊 推文信息: {text}")
                                break
                except:
                    pass
        
        except Exception as e:
            print(f"检查账号时出错: {e}")
        
        input("\n按回车键关闭浏览器...")
        
    except Exception as e:
        print(f"启动浏览器失败: {e}")
        print("尝试直接在默认浏览器中打开...")
        webbrowser.open(tweet_url)
        input("请在浏览器中查看推文，然后按回车键继续...")
        webbrowser.open(account_url)
        input("请在浏览器中查看账号，然后按回车键结束...")
        return
    
    finally:
        try:
            driver.quit()
        except:
            pass

def manual_check():
    """手动检查"""
    tweet_url = "https://x.com/AnthonyGon78058/status/1952045432521232414"
    account_url = "https://x.com/AnthonyGon78058"
    
    print("=" * 60)
    print("手动检查模式")
    print("=" * 60)
    print("将在默认浏览器中打开链接，请手动检查")
    print()
    
    print("1. 打开推文链接...")
    webbrowser.open(tweet_url)
    input("请检查推文是否存在，然后按回车键继续...")
    
    print("\n2. 打开账号链接...")
    webbrowser.open(account_url)
    input("请检查账号状态，然后按回车键结束...")

if __name__ == "__main__":
    print("选择检查方式:")
    print("1. 自动检查 (使用Selenium)")
    print("2. 手动检查 (在默认浏览器中打开)")
    
    choice = input("请输入选择 (1 或 2): ").strip()
    
    if choice == "1":
        check_tweet_simple()
    elif choice == "2":
        manual_check()
    else:
        print("无效选择，使用手动检查模式")
        manual_check()
