#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试文案输入修复效果
验证文案是否会在输入完成处理后被清空
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.core.browser_manager import BrowserPool
from src.modules.posting.executor import PostingExecutor
from src.database.connection import init_database
from src.utils.logger import get_logger

logger = get_logger(__name__)

async def test_text_input_fix():
    """测试文案输入修复效果"""
    browser_pool = BrowserPool()
    
    try:
        # 创建浏览器实例
        driver_wrapper = await browser_pool.get_driver(
            account_id=999,  # 临时ID
            headless=False,  # 显示浏览器以便观察
            proxy=None
        )
        
        # 访问X平台
        logger.info("访问X平台...")
        await driver_wrapper.get("https://x.com")
        await asyncio.sleep(3)
        
        # 创建PostingExecutor实例
        posting_executor = PostingExecutor()
        
        # 测试文案
        test_texts = [
            "这是一条测试推文，看看能否正常显示 😊",
            "🔥 😍 💖 🌟\n\n又是美好的一天，在线中....\n\n🌟💯",
            "纯文本测试，不使用表情符号",
            "Hello everyone! 🌟 大家今天过得怎么样？"
        ]
        
        for i, test_text in enumerate(test_texts, 1):
            print(f"\n{'='*60}")
            print(f"测试 {i}/{len(test_texts)}: {test_text[:30]}...")
            print('='*60)
            
            try:
                # 查找推文输入框
                text_area_selectors = [
                    '[data-testid="tweetTextarea_0"]',
                    '[data-testid="tweetTextarea"]',
                    '[role="textbox"][aria-label*="Tweet"]',
                    '[role="textbox"][aria-label*="Post"]',
                    'div[contenteditable="true"]'
                ]
                
                text_area = None
                selected_selector = None
                
                for selector in text_area_selectors:
                    try:
                        elements = await driver_wrapper.find_elements(
                            driver_wrapper.By.CSS_SELECTOR, selector
                        )
                        if elements:
                            text_area = elements[0]
                            selected_selector = selector
                            logger.info(f"✅ 找到文本框: {selector}")
                            break
                    except:
                        continue
                
                if not text_area:
                    logger.warning("❌ 未找到文本输入框，跳过此测试")
                    continue
                
                # 清空输入框
                await driver_wrapper.execute_async(text_area.clear)
                await asyncio.sleep(0.5)
                
                # 使用修复后的输入方法
                logger.info(f"📝 开始输入文案: '{test_text}'")
                await posting_executor._input_text_content(driver_wrapper, test_text)
                
                # 等待一段时间观察
                await asyncio.sleep(2)
                
                # 验证文案是否还在
                final_text = await driver_wrapper.execute_async(
                    lambda: driver_wrapper.driver.execute_script(
                        'return arguments[0].value || arguments[0].textContent || arguments[0].innerText || "";',
                        text_area
                    )
                )
                
                print(f"📋 最终文案: '{final_text}'")
                print(f"📏 预期长度: {len(test_text)}, 实际长度: {len(final_text) if final_text else 0}")
                
                if final_text and test_text.strip() in final_text:
                    print("✅ 文案保持完整，修复成功！")
                elif final_text and len(final_text.strip()) > 0:
                    print("⚠️ 文案部分保留，可能有轻微问题")
                else:
                    print("❌ 文案被清空，问题仍然存在")
                
                # 清空准备下一次测试
                await driver_wrapper.execute_async(text_area.clear)
                await asyncio.sleep(1)
                
            except Exception as e:
                logger.error(f"测试 {i} 失败: {e}")
        
        print(f"\n{'='*60}")
        print("测试完成！请观察浏览器中的效果")
        print('='*60)
        input("按回车键关闭浏览器...")
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
    finally:
        await browser_pool.release_driver(999)

async def main():
    """主函数"""
    # 初始化数据库
    init_database()
    
    print("=" * 60)
    print("文案输入修复测试")
    print("=" * 60)
    print("此测试将验证修复后的文案输入是否会被意外清空")
    print()
    
    await test_text_input_fix()

if __name__ == "__main__":
    asyncio.run(main())
